"""
Tests for US-07: Suggestions
"""

import pytest
import requests
import json
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8007"

class TestSuggestions:
    """Test class for suggestions functionality"""
    
    @classmethod
    def setup_class(cls):
        """Setup test class"""
        cls.suggestion_id = None
    
    def test_01_health_check(self):
        """Test health check endpoint"""
        response = requests.get(f"{BASE_URL}/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "US-07" in data["service"]
        print("✓ Health check passed")
    
    def test_02_root_endpoint(self):
        """Test root endpoint"""
        response = requests.get(f"{BASE_URL}/")
        
        assert response.status_code == 200
        data = response.json()
        assert "US-07" in data["message"]
        assert "endpoints" in data
        print("✓ Root endpoint working")
    
    def test_03_generate_suggestions_with_ids(self):
        """Test suggestions generation with resume and job IDs"""
        payload = {
            "resume_id": "test-resume-123",
            "job_description_id": "test-job-456"
        }
        
        response = requests.post(f"{BASE_URL}/generate", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        assert "suggestion_id" in data
        assert data["resume_id"] == "test-resume-123"
        assert data["job_description_id"] == "test-job-456"
        assert "suggestions" in data
        assert "missing_keywords" in data
        assert "priority_skills" in data
        assert "quick_wins" in data
        assert "overall_match_score" in data
        
        # Validate suggestions are limited to 5
        assert len(data["suggestions"]) <= 5
        
        # Store suggestion ID for later tests
        self.__class__.suggestion_id = data["suggestion_id"]
        print(f"✓ Generated {len(data['suggestions'])} suggestions")
        print(f"  Suggestion ID: {self.suggestion_id}")
    
    def test_04_suggestions_structure_validation(self):
        """Test suggestions structure validation"""
        payload = {
            "resume_id": "structure-test-resume",
            "job_description_id": "structure-test-job"
        }
        
        response = requests.post(f"{BASE_URL}/generate", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        suggestions = data["suggestions"]
        
        # Validate each suggestion structure
        for suggestion in suggestions:
            assert "category" in suggestion
            assert "priority" in suggestion
            assert "title" in suggestion
            assert "description" in suggestion
            assert "action_items" in suggestion
            assert "impact_score" in suggestion
            
            # Validate priority values
            assert suggestion["priority"] in ["high", "medium", "low"]
            
            # Validate impact score range
            assert 0 <= suggestion["impact_score"] <= 1
            
            # Validate action items is a list
            assert isinstance(suggestion["action_items"], list)
            assert len(suggestion["action_items"]) > 0
        
        print("✓ Suggestions structure validated")
        print(f"  Categories: {set(s['category'] for s in suggestions)}")
        print(f"  Priorities: {set(s['priority'] for s in suggestions)}")
    
    def test_05_priority_based_suggestions(self):
        """Test priority-based suggestions generation"""
        payload = {
            "resume_id": "priority-test-resume",
            "job_description_id": "priority-test-job"
        }
        
        response = requests.post(f"{BASE_URL}/generate", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        suggestions = data["suggestions"]
        
        # Check that high priority suggestions come first
        priorities = [s["priority"] for s in suggestions]
        high_priority_indices = [i for i, p in enumerate(priorities) if p == "high"]
        medium_priority_indices = [i for i, p in enumerate(priorities) if p == "medium"]
        
        # High priority suggestions should come before medium priority
        if high_priority_indices and medium_priority_indices:
            assert max(high_priority_indices) < min(medium_priority_indices)
        
        print("✓ Priority-based ordering validated")
        print(f"  High priority: {len(high_priority_indices)}")
        print(f"  Medium priority: {len(medium_priority_indices)}")
    
    def test_06_quick_wins_generation(self):
        """Test quick wins generation"""
        payload = {
            "resume_id": "quickwins-test-resume",
            "job_description_id": "quickwins-test-job"
        }
        
        response = requests.post(f"{BASE_URL}/generate", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        quick_wins = data["quick_wins"]
        
        assert isinstance(quick_wins, list)
        assert len(quick_wins) <= 5  # Should be limited to 5
        
        # Each quick win should be a non-empty string
        for quick_win in quick_wins:
            assert isinstance(quick_win, str)
            assert len(quick_win) > 0
        
        print(f"✓ Generated {len(quick_wins)} quick wins")
        for i, qw in enumerate(quick_wins, 1):
            print(f"  {i}. {qw}")
    
    def test_07_priority_skills_identification(self):
        """Test priority skills identification"""
        payload = {
            "resume_id": "skills-test-resume",
            "job_description_id": "skills-test-job"
        }
        
        response = requests.post(f"{BASE_URL}/generate", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        priority_skills = data["priority_skills"]
        
        assert isinstance(priority_skills, list)
        assert len(priority_skills) <= 5  # Should be limited to 5
        
        # Each skill should be a non-empty string
        for skill in priority_skills:
            assert isinstance(skill, str)
            assert len(skill) > 0
        
        print(f"✓ Identified {len(priority_skills)} priority skills")
        print(f"  Skills: {priority_skills}")
    
    def test_08_missing_keywords_analysis(self):
        """Test missing keywords analysis"""
        payload = {
            "resume_id": "keywords-test-resume",
            "job_description_id": "keywords-test-job"
        }
        
        response = requests.post(f"{BASE_URL}/generate", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        missing_keywords = data["missing_keywords"]
        
        assert isinstance(missing_keywords, list)
        assert len(missing_keywords) <= 10  # Should be limited to 10 for display
        
        # Each keyword should be a non-empty string
        for keyword in missing_keywords:
            assert isinstance(keyword, str)
            assert len(keyword) > 0
        
        print(f"✓ Found {len(missing_keywords)} missing keywords")
        print(f"  Keywords: {missing_keywords[:5]}")  # Show first 5
    
    def test_09_suggestion_categories(self):
        """Test different suggestion categories"""
        payload = {
            "resume_id": "categories-test-resume",
            "job_description_id": "categories-test-job"
        }
        
        response = requests.post(f"{BASE_URL}/generate", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        suggestions = data["suggestions"]
        categories = set(s["category"] for s in suggestions)
        
        # Expected categories
        expected_categories = {"Keywords", "Technical Skills", "Score Improvement"}
        
        # Should have at least one category
        assert len(categories) > 0
        
        # All categories should be from expected set
        assert categories.issubset(expected_categories)
        
        print(f"✓ Suggestion categories: {categories}")
    
    def test_10_get_suggestion_history(self):
        """Test getting suggestion history"""
        response = requests.get(f"{BASE_URL}/history")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0  # Should have suggestions from previous tests
        
        # Check structure of first suggestion
        first_suggestion = data[0]
        assert "id" in first_suggestion
        assert "resume_id" in first_suggestion
        assert "job_description_id" in first_suggestion
        assert "suggestion_count" in first_suggestion
        assert "high_priority_count" in first_suggestion
        assert "created_at" in first_suggestion
        
        print(f"✓ Listed {len(data)} suggestion histories")
    
    def test_11_get_suggestion_details(self):
        """Test getting detailed suggestion results"""
        if not self.suggestion_id:
            pytest.skip("No suggestion ID available from previous tests")
        
        response = requests.get(f"{BASE_URL}/suggestion/{self.suggestion_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == self.suggestion_id
        assert "suggestions" in data
        assert "missing_keywords" in data
        assert "priority_skills" in data
        assert "quick_wins" in data
        assert "overall_match_score" in data
        assert "created_at" in data
        
        print("✓ Retrieved suggestion details successfully")
    
    def test_12_get_nonexistent_suggestion(self):
        """Test getting details for non-existent suggestion"""
        fake_id = "non-existent-suggestion-id"
        response = requests.get(f"{BASE_URL}/suggestion/{fake_id}")
        
        assert response.status_code == 404
        print("✓ Non-existent suggestion handling working")
    
    def test_13_invalid_request_validation(self):
        """Test invalid request validation"""
        # Test with no IDs provided
        payload = {}
        
        response = requests.post(f"{BASE_URL}/generate", json=payload)
        
        assert response.status_code == 400
        print("✓ Invalid request validation working")
    
    def test_14_suggestion_impact_scores(self):
        """Test suggestion impact scores"""
        payload = {
            "resume_id": "impact-test-resume",
            "job_description_id": "impact-test-job"
        }
        
        response = requests.post(f"{BASE_URL}/generate", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        suggestions = data["suggestions"]
        
        # Validate impact scores
        for suggestion in suggestions:
            impact_score = suggestion["impact_score"]
            assert 0 <= impact_score <= 1
            
            # High priority suggestions should generally have higher impact scores
            if suggestion["priority"] == "high":
                assert impact_score >= 0.5
        
        print("✓ Impact scores validated")
    
    def test_15_action_items_quality(self):
        """Test quality of action items"""
        payload = {
            "resume_id": "action-test-resume",
            "job_description_id": "action-test-job"
        }
        
        response = requests.post(f"{BASE_URL}/generate", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        suggestions = data["suggestions"]
        
        for suggestion in suggestions:
            action_items = suggestion["action_items"]
            
            # Should have at least one action item
            assert len(action_items) > 0
            
            # Each action item should be meaningful (not too short)
            for action in action_items:
                assert len(action) > 10  # At least 10 characters
                assert action.strip() == action  # No leading/trailing whitespace
        
        print("✓ Action items quality validated")
    
    def test_16_consistency_check(self):
        """Test consistency of suggestions for same input"""
        payload = {
            "resume_id": "consistency-test-resume",
            "job_description_id": "consistency-test-job"
        }
        
        # Generate suggestions twice
        response1 = requests.post(f"{BASE_URL}/generate", json=payload)
        response2 = requests.post(f"{BASE_URL}/generate", json=payload)
        
        assert response1.status_code == 200
        assert response2.status_code == 200
        
        data1 = response1.json()
        data2 = response2.json()
        
        # Should have same number of suggestions
        assert len(data1["suggestions"]) == len(data2["suggestions"])
        
        # Should have same categories (order might differ)
        categories1 = set(s["category"] for s in data1["suggestions"])
        categories2 = set(s["category"] for s in data2["suggestions"])
        assert categories1 == categories2
        
        print("✓ Consistency check passed")

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
