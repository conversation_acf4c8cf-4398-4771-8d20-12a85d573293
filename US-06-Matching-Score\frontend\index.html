<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>US-06: Matching Score</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-align: center;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .card h2 {
            color: #28a745;
            margin-bottom: 1rem;
            border-bottom: 2px solid #28a745;
            padding-bottom: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #555;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #28a745;
        }

        .btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .score-display {
            text-align: center;
            margin: 2rem 0;
        }

        .overall-score {
            font-size: 4rem;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 0.5rem;
        }

        .score-label {
            font-size: 1.2rem;
            color: #666;
        }

        .score-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .score-item {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #28a745;
        }

        .score-value {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
        }

        .score-name {
            color: #666;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin-top: 0.5rem;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .results-section {
            margin-top: 2rem;
        }

        .section-title {
            color: #28a745;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 0.5rem;
        }

        .keyword-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .keyword-tag {
            background: #28a745;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .missing-keyword {
            background: #dc3545;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .skill-category {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .skill-category h4 {
            color: #28a745;
            margin-bottom: 0.5rem;
        }

        .recommendations {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 1rem;
            border-radius: 5px;
        }

        .recommendation-item {
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background: white;
            border-radius: 3px;
            border-left: 3px solid #007bff;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #28a745;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #dc3545;
            margin-top: 1rem;
        }

        .tabs {
            display: flex;
            margin-bottom: 2rem;
            border-bottom: 2px solid #ddd;
        }

        .tab {
            padding: 1rem 2rem;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab.active {
            border-bottom-color: #28a745;
            color: #28a745;
            font-weight: bold;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .history-table th,
        .history-table td {
            padding: 0.75rem;
            border: 1px solid #ddd;
            text-align: left;
        }

        .history-table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .history-table tr:hover {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Matching Score Calculator</h1>
            <p>Calculate compatibility scores between resumes and job descriptions</p>
        </div>

        <div class="tabs">
            <div class="tab active" onclick="switchTab('calculate')">Calculate Match</div>
            <div class="tab" onclick="switchTab('history')">Match History</div>
        </div>

        <!-- Calculate Match Tab -->
        <div id="calculate-tab" class="tab-content active">
            <div class="card">
                <h2>🎯 Calculate Matching Score</h2>
                <form id="matchingForm">
                    <div class="form-group">
                        <label for="resumeId">Resume ID:</label>
                        <input type="text" id="resumeId" placeholder="Enter resume document ID" required>
                    </div>
                    <div class="form-group">
                        <label for="jobId">Job Description ID:</label>
                        <input type="text" id="jobId" placeholder="Enter job description document ID" required>
                    </div>
                    <button type="submit" class="btn">Calculate Match Score</button>
                </form>
            </div>
        </div>

        <!-- History Tab -->
        <div id="history-tab" class="tab-content">
            <div class="card">
                <h2>📈 Match History</h2>
                <button onclick="loadHistory()" class="btn">Refresh History</button>
                <div id="historyResults"></div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="results" class="results-section"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8006';

        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(`${tabName}-tab`).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Form submission
        document.getElementById('matchingForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const resumeId = document.getElementById('resumeId').value;
            const jobId = document.getElementById('jobId').value;
            
            showLoading();
            
            try {
                const response = await fetch(`${API_BASE}/calculate-match`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        resume_id: resumeId,
                        job_description_id: jobId
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                displayResults(data);
                
            } catch (error) {
                showError(`Error: ${error.message}`);
            }
        });

        function showLoading() {
            document.getElementById('results').innerHTML = `
                <div class="loading">
                    <h3>🔄 Calculating Match Score...</h3>
                    <p>Please wait while we analyze the compatibility.</p>
                </div>
            `;
        }

        function showError(message) {
            document.getElementById('results').innerHTML = `
                <div class="error">
                    <strong>Error:</strong> ${message}
                </div>
            `;
        }

        function getScoreColor(score) {
            if (score >= 90) return '#28a745';
            if (score >= 75) return '#20c997';
            if (score >= 60) return '#ffc107';
            if (score >= 45) return '#fd7e14';
            return '#dc3545';
        }

        function getScoreLabel(score) {
            if (score >= 90) return 'Excellent Match';
            if (score >= 75) return 'Good Match';
            if (score >= 60) return 'Fair Match';
            if (score >= 45) return 'Poor Match';
            return 'Very Poor Match';
        }

        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            let html = `
                <div class="card">
                    <h2>📊 Match Results</h2>
                    <p><strong>Match ID:</strong> ${data.match_id}</p>
                    <p><strong>Resume ID:</strong> ${data.resume_id}</p>
                    <p><strong>Job Description ID:</strong> ${data.job_description_id}</p>
                    <p><strong>Calculated:</strong> ${new Date(data.created_at).toLocaleString()}</p>
                    
                    <div class="score-display">
                        <div class="overall-score" style="color: ${getScoreColor(data.overall_score)}">${data.overall_score}%</div>
                        <div class="score-label">${getScoreLabel(data.overall_score)}</div>
                    </div>
            `;

            // Detailed Scores
            html += `
                <div class="section-title">📈 Detailed Score Breakdown</div>
                <div class="score-grid">
            `;

            for (const [scoreName, scoreValue] of Object.entries(data.detailed_scores)) {
                const displayName = scoreName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                html += `
                    <div class="score-item">
                        <div class="score-value" style="color: ${getScoreColor(scoreValue)}">${scoreValue}%</div>
                        <div class="score-name">${displayName}</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${scoreValue}%; background: ${getScoreColor(scoreValue)}"></div>
                        </div>
                    </div>
                `;
            }

            html += `</div>`;

            // Matching Keywords
            if (data.matching_keywords && data.matching_keywords.length > 0) {
                html += `
                    <div class="section-title">✅ Matching Keywords</div>
                    <div class="keyword-list">
                        ${data.matching_keywords.map(keyword => `<span class="keyword-tag">${keyword}</span>`).join('')}
                    </div>
                `;
            }

            // Missing Keywords
            if (data.missing_keywords && data.missing_keywords.length > 0) {
                html += `
                    <div class="section-title">❌ Missing Keywords</div>
                    <div class="keyword-list">
                        ${data.missing_keywords.map(keyword => `<span class="missing-keyword">${keyword}</span>`).join('')}
                    </div>
                `;
            }

            // Skill Matches
            if (data.skill_matches && Object.keys(data.skill_matches).length > 0) {
                html += `<div class="section-title">💪 Skill Matches</div>`;
                
                for (const [category, skills] of Object.entries(data.skill_matches)) {
                    html += `
                        <div class="skill-category">
                            <h4>${category.replace(/_/g, ' ').toUpperCase()}</h4>
                            <div class="keyword-list">
                                ${skills.map(skill => `<span class="keyword-tag">${skill}</span>`).join('')}
                            </div>
                        </div>
                    `;
                }
            }

            // Skill Gaps
            if (data.skill_gaps && Object.keys(data.skill_gaps).length > 0) {
                html += `<div class="section-title">🎯 Skill Gaps</div>`;
                
                for (const [category, gaps] of Object.entries(data.skill_gaps)) {
                    html += `
                        <div class="skill-category">
                            <h4>${category.replace(/_/g, ' ').toUpperCase()}</h4>
                            <div class="keyword-list">
                                ${gaps.map(gap => `<span class="missing-keyword">${gap}</span>`).join('')}
                            </div>
                        </div>
                    `;
                }
            }

            // Recommendations
            if (data.recommendations && data.recommendations.length > 0) {
                html += `
                    <div class="section-title">💡 Recommendations</div>
                    <div class="recommendations">
                        ${data.recommendations.map((rec, index) => 
                            `<div class="recommendation-item">${index + 1}. ${rec}</div>`
                        ).join('')}
                    </div>
                `;
            }

            html += `</div>`;
            resultsDiv.innerHTML = html;
        }

        async function loadHistory() {
            try {
                const response = await fetch(`${API_BASE}/matches`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const matches = await response.json();
                
                let html = `
                    <div class="section-title">📋 Recent Matches (${matches.length})</div>
                `;
                
                if (matches.length === 0) {
                    html += `<p>No matches found.</p>`;
                } else {
                    html += `
                        <table class="history-table">
                            <thead>
                                <tr>
                                    <th>Match ID</th>
                                    <th>Resume ID</th>
                                    <th>Job Description ID</th>
                                    <th>Score</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;
                    
                    matches.forEach(match => {
                        html += `
                            <tr>
                                <td style="font-family: monospace;">${match.id.substring(0, 8)}...</td>
                                <td>${match.resume_id}</td>
                                <td>${match.job_description_id}</td>
                                <td>
                                    <span style="color: ${getScoreColor(match.overall_score)}; font-weight: bold;">
                                        ${match.overall_score}%
                                    </span>
                                </td>
                                <td>${new Date(match.created_at).toLocaleString()}</td>
                                <td>
                                    <button onclick="viewMatchDetails('${match.id}')" class="btn" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                                        View Details
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += `</tbody></table>`;
                }
                
                document.getElementById('historyResults').innerHTML = html;
                
            } catch (error) {
                document.getElementById('historyResults').innerHTML = `
                    <div class="error">
                        Error loading history: ${error.message}
                    </div>
                `;
            }
        }

        async function viewMatchDetails(matchId) {
            try {
                const response = await fetch(`${API_BASE}/match/${matchId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Switch to calculate tab and display results
                switchTab('calculate');
                displayResults(data);
                
            } catch (error) {
                alert(`Error loading match details: ${error.message}`);
            }
        }

        // Load history on page load
        document.addEventListener('DOMContentLoaded', () => {
            // Auto-load history when switching to history tab
            const historyTab = document.querySelector('.tab:nth-child(2)');
            historyTab.addEventListener('click', loadHistory);
        });
    </script>
</body>
</html>
