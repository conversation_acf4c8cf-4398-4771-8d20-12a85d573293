"""
Database initialization for US-05: Keyword Extraction & Parsing
"""

import sqlite3
import os

def init_keyword_extraction_db():
    """Initialize the keyword extraction database"""
    
    # Create database directory if it doesn't exist
    db_dir = os.path.dirname(__file__)
    os.makedirs(db_dir, exist_ok=True)
    
    # Database path
    db_path = os.path.join(db_dir, "keyword_extraction.db")
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Create keyword_analyses table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS keyword_analyses (
                id TEXT PRIMARY KEY,
                document_id TEXT,
                document_type TEXT,
                analysis_type TEXT NOT NULL,
                results_json TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_keyword_analyses_document_id ON keyword_analyses(document_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_keyword_analyses_type ON keyword_analyses(analysis_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_keyword_analyses_created_at ON keyword_analyses(created_at)')
        
        conn.commit()
        print(f"Keyword extraction database initialized successfully at: {db_path}")
        
    except Exception as e:
        print(f"Error initializing keyword extraction database: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    init_keyword_extraction_db()
