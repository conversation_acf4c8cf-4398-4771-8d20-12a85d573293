# US-06: Matching Score

## Overview
This service calculates match scores between resumes and job descriptions by analyzing keyword overlap, skill compatibility, and role alignment. It provides detailed scoring breakdowns and actionable recommendations for improvement.

## Features
- **Overall Match Score**: Calculate a comprehensive score (0-100) comparing resume and job description
- **Detailed Score Breakdown**: Separate scores for keywords, skills, roles, and technical categories
- **Skill Gap Analysis**: Identify missing skills and matching competencies
- **Keyword Matching**: Compare keyword overlap and identify missing terms
- **Recommendations**: Generate actionable suggestions for improving match scores
- **Match History**: Track and retrieve previous matching calculations

## API Endpoints

### Health Check
- **GET** `/health` - Service health check
- **GET** `/` - Root endpoint with service information

### Matching Score
- **POST** `/calculate-match` - Calculate match score between resume and job description
- **GET** `/matches` - Get matching history
- **GET** `/match/{match_id}` - Get detailed match results

## Request/Response Examples

### Calculate Match Score
```json
POST /calculate-match
{
    "resume_id": "resume-123",
    "job_description_id": "job-456"
}
```

Response:
```json
{
    "match_id": "uuid-here",
    "resume_id": "resume-123",
    "job_description_id": "job-456",
    "overall_score": 78.5,
    "detailed_scores": {
        "keyword_match": 85.0,
        "skill_match": 72.0,
        "role_match": 90.0,
        "technical_skills": 80.0,
        "frameworks": 65.0,
        "tools": 75.0
    },
    "matching_keywords": ["python", "developer", "experience"],
    "missing_keywords": ["senior", "leadership", "architecture"],
    "skill_matches": {
        "programming_languages": ["python", "javascript"],
        "frameworks": ["django", "react"]
    },
    "skill_gaps": {
        "cloud_platforms": ["kubernetes"],
        "tools": ["jenkins"]
    },
    "recommendations": [
        "Consider learning Cloud Platforms: kubernetes",
        "Include these relevant keywords: senior, leadership",
        "Add Tools experience: jenkins"
    ],
    "created_at": "2024-01-15T10:30:00"
}
```

## Scoring Algorithm

### Overall Score Calculation
The overall score is a weighted combination of:
- **Skills Match (50%)**: Technical and soft skills compatibility
- **Keywords Match (30%)**: Keyword overlap between documents
- **Role Match (20%)**: Job role and position alignment

### Detailed Scores
- **Keyword Match**: Percentage of job description keywords found in resume
- **Skill Match**: Overall technical skills compatibility
- **Role Match**: Job title and position alignment
- **Technical Skills**: Programming languages compatibility
- **Frameworks**: Framework and library matches
- **Tools**: Development tools and software matches

### Score Ranges
- **90-100**: Excellent match - highly qualified candidate
- **75-89**: Good match - strong candidate with minor gaps
- **60-74**: Fair match - candidate needs some development
- **45-59**: Poor match - significant skill gaps
- **0-44**: Very poor match - major misalignment

## Integration with US-05

This service integrates with US-05 (Keyword Extraction) to:
1. Retrieve keyword analysis for both resume and job description
2. Extract technical skills, soft skills, and job roles
3. Use TF-IDF keywords for matching calculations
4. Fallback to mock data if US-05 service is unavailable

## Installation & Setup

### Prerequisites
- Python 3.8+
- US-05 Keyword Extraction service running on port 8005

### Install Dependencies
```bash
cd US-06-Matching-Score/backend
pip install -r requirements.txt
```

### Initialize Database
```bash
cd ../database
python init_db.py
```

### Run the Service
```bash
cd ../backend
python app.py
```

The service will be available at `http://localhost:8006`

## Testing

### Run Tests
```bash
cd ../tests
python -m pytest test_matching_score.py -v
```

### Test Coverage
- Health check and root endpoints
- Match score calculation
- Detailed scores validation
- Skill matching analysis
- Keyword analysis
- Recommendations generation
- Match history retrieval
- Edge cases and error handling

## Database Schema

### matching_results Table
```sql
CREATE TABLE matching_results (
    id TEXT PRIMARY KEY,
    resume_id TEXT NOT NULL,
    job_description_id TEXT NOT NULL,
    overall_score REAL NOT NULL,
    detailed_scores TEXT,
    matching_keywords TEXT,
    missing_keywords TEXT,
    skill_matches TEXT,
    skill_gaps TEXT,
    recommendations TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Recommendation Engine

### Types of Recommendations
1. **Skill Development**: Suggests missing technical skills to learn
2. **Keyword Optimization**: Recommends keywords to include in resume
3. **Experience Highlighting**: Advises on emphasizing relevant experience
4. **General Improvements**: Provides overall enhancement suggestions

### Recommendation Prioritization
- **High Priority**: Critical missing skills (programming languages, frameworks)
- **Medium Priority**: Important tools and platforms
- **Low Priority**: Nice-to-have skills and keywords

## Configuration
- **Port**: 8006 (configurable)
- **Database**: SQLite (matching_score.db)
- **US-05 Integration**: http://localhost:8005
- **Max Recommendations**: 5 per analysis
- **Score Precision**: 2 decimal places

## Error Handling
- Graceful fallback when US-05 service is unavailable
- Input validation for document IDs
- Database connection error handling
- HTTP status codes for different error types

## Performance Considerations
- Caching of keyword analyses
- Database indexing for fast retrieval
- Asynchronous processing for large documents
- Efficient similarity calculations

## Future Enhancements
- Machine learning-based scoring models
- Industry-specific matching algorithms
- Real-time score updates
- Batch processing capabilities
- Advanced recommendation engine
- Integration with external job databases
