# US-07: Suggestions

## Overview
This service generates actionable improvement suggestions based on resume-job description analysis. It provides prioritized recommendations to help candidates improve their match scores and enhance their resumes for specific job opportunities.

## Features
- **Intelligent Suggestions**: Generate up to 5 prioritized improvement suggestions
- **Multiple Categories**: Keywords, technical skills, and score improvement suggestions
- **Priority Ranking**: High, medium, and low priority suggestions with impact scores
- **Quick Wins**: Immediate actions candidates can take to improve their profiles
- **Priority Skills**: Identify the most important skills to develop
- **Action Items**: Specific, actionable steps for each suggestion
- **Suggestion History**: Track and retrieve previous suggestion analyses

## API Endpoints

### Health Check
- **GET** `/health` - Service health check
- **GET** `/` - Root endpoint with service information

### Suggestions
- **POST** `/generate` - Generate improvement suggestions
- **GET** `/history` - Get suggestion history
- **GET** `/suggestion/{suggestion_id}` - Get detailed suggestion results

## Request/Response Examples

### Generate Suggestions
```json
POST /generate
{
    "resume_id": "resume-123",
    "job_description_id": "job-456"
}
```

Or using existing match:
```json
POST /generate
{
    "match_id": "match-789"
}
```

Response:
```json
{
    "suggestion_id": "uuid-here",
    "resume_id": "resume-123",
    "job_description_id": "job-456",
    "overall_match_score": 65.5,
    "suggestions": [
        {
            "category": "Technical Skills",
            "priority": "high",
            "title": "Develop Cloud Platforms Skills",
            "description": "The job requires cloud platforms skills that are not evident in your resume.",
            "action_items": [
                "Learn kubernetes",
                "Learn docker",
                "Take online courses in Cloud Platforms",
                "Build projects using Cloud Platforms",
                "Consider certifications in Cloud Platforms"
            ],
            "impact_score": 0.9
        },
        {
            "category": "Keywords",
            "priority": "high",
            "title": "Add Critical Missing Keywords",
            "description": "Your resume is missing 3 important keywords that appear in the job description.",
            "action_items": [
                "Include 'senior' in your resume where relevant",
                "Include 'leadership' in your resume where relevant",
                "Review the job description and identify where these keywords naturally fit"
            ],
            "impact_score": 0.8
        }
    ],
    "missing_keywords": ["senior", "leadership", "architecture", "microservices"],
    "priority_skills": ["kubernetes", "docker", "react", "angular"],
    "quick_wins": [
        "Add 'senior' to your skills section",
        "Include 'leadership' in your summary",
        "Mention experience with kubernetes if applicable",
        "Update your resume summary to match job requirements",
        "Use action verbs that appear in the job description"
    ],
    "created_at": "2024-01-15T10:30:00"
}
```

## Suggestion Categories

### 1. Keywords
- **Purpose**: Improve keyword matching with job descriptions
- **Focus**: Missing important terms and phrases
- **Actions**: Include specific keywords in resume sections

### 2. Technical Skills
- **Purpose**: Address skill gaps in technical competencies
- **Focus**: Programming languages, frameworks, tools, platforms
- **Actions**: Learning recommendations and skill development

### 3. Score Improvement
- **Purpose**: Enhance specific scoring areas
- **Focus**: Low-performing categories in detailed scores
- **Actions**: Better presentation and quantification of experience

## Priority Levels

### High Priority (Impact Score: 0.7-1.0)
- Critical missing skills (programming languages, frameworks)
- Important keywords with high frequency in job description
- Major score deficiencies (< 30%)

### Medium Priority (Impact Score: 0.4-0.6)
- Secondary skills and tools
- Moderate keyword gaps
- Score improvements (30-60%)

### Low Priority (Impact Score: 0.1-0.3)
- Nice-to-have skills
- Minor keyword additions
- Minor presentation improvements

## Quick Wins
Immediate actions candidates can take:
1. **Keyword Integration**: Add missing keywords to existing sections
2. **Skills Section Updates**: Include relevant technical skills
3. **Summary Optimization**: Tailor resume summary to job requirements
4. **Action Verb Usage**: Use job description language
5. **Quantification**: Add metrics to achievements

## Priority Skills
Top 5 most important skills to develop based on:
- High-impact skill categories (programming languages, frameworks)
- Frequency in job requirements
- Current skill gaps

## Integration with US-06

This service integrates with US-06 (Matching Score) to:
1. Retrieve detailed match analysis results
2. Use skill gaps and missing keywords for suggestions
3. Analyze detailed scores for improvement areas
4. Create new match analysis if needed
5. Fallback to mock data if US-06 service is unavailable

## Installation & Setup

### Prerequisites
- Python 3.8+
- US-06 Matching Score service running on port 8006

### Install Dependencies
```bash
cd US-07-Suggestions/backend
pip install -r requirements.txt
```

### Initialize Database
```bash
cd ../database
python init_db.py
```

### Run the Service
```bash
cd ../backend
python app.py
```

The service will be available at `http://localhost:8007`

## Testing

### Run Tests
```bash
cd ../tests
python -m pytest test_suggestions.py -v
```

### Test Coverage
- Health check and root endpoints
- Suggestion generation with different inputs
- Suggestion structure validation
- Priority-based ordering
- Quick wins generation
- Priority skills identification
- Missing keywords analysis
- Suggestion categories
- History retrieval
- Edge cases and error handling

## Database Schema

### suggestion_results Table
```sql
CREATE TABLE suggestion_results (
    id TEXT PRIMARY KEY,
    match_id TEXT,
    resume_id TEXT,
    job_description_id TEXT,
    overall_match_score REAL,
    suggestions_json TEXT,
    missing_keywords TEXT,
    priority_skills TEXT,
    quick_wins TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Suggestion Algorithm

### 1. Data Collection
- Retrieve match analysis from US-06
- Extract missing keywords, skill gaps, and detailed scores
- Identify areas for improvement

### 2. Suggestion Generation
- **Keyword Suggestions**: Based on missing important terms
- **Skill Suggestions**: Based on technical skill gaps
- **Score Suggestions**: Based on low-performing areas

### 3. Prioritization
- Sort by priority level (high > medium > low)
- Sort by impact score within same priority
- Limit to top 5 suggestions

### 4. Action Item Generation
- Specific, actionable steps for each suggestion
- Learning resources and development paths
- Resume optimization techniques

## Configuration
- **Port**: 8007 (configurable)
- **Database**: SQLite (suggestions.db)
- **US-06 Integration**: http://localhost:8006
- **Max Suggestions**: 5 per analysis
- **Max Quick Wins**: 5 per analysis
- **Max Priority Skills**: 5 per analysis

## Error Handling
- Graceful fallback when US-06 service is unavailable
- Input validation for request parameters
- Database connection error handling
- HTTP status codes for different error types

## Performance Considerations
- Caching of match analyses
- Database indexing for fast retrieval
- Efficient suggestion algorithms
- Minimal external service dependencies

## Future Enhancements
- Machine learning-based suggestion ranking
- Industry-specific recommendations
- Personalized learning paths
- Integration with learning platforms
- A/B testing for suggestion effectiveness
- Real-time suggestion updates
- Advanced natural language generation for suggestions
