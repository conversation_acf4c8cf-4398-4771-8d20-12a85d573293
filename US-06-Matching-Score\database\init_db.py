"""
Database initialization for US-06: Matching Score
"""

import sqlite3
import os

def init_matching_score_db():
    """Initialize the matching score database"""
    
    # Create database directory if it doesn't exist
    db_dir = os.path.dirname(__file__)
    os.makedirs(db_dir, exist_ok=True)
    
    # Database path
    db_path = os.path.join(db_dir, "matching_score.db")
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Create matching_results table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS matching_results (
                id TEXT PRIMARY KEY,
                resume_id TEXT NOT NULL,
                job_description_id TEXT NOT NULL,
                overall_score REAL NOT NULL,
                detailed_scores TEXT,
                matching_keywords TEXT,
                missing_keywords TEXT,
                skill_matches TEXT,
                skill_gaps TEXT,
                recommendations TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_matching_resume_id ON matching_results(resume_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_matching_job_id ON matching_results(job_description_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_matching_score ON matching_results(overall_score)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_matching_created_at ON matching_results(created_at)')
        
        conn.commit()
        print(f"Matching score database initialized successfully at: {db_path}")
        
    except Exception as e:
        print(f"Error initializing matching score database: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    init_matching_score_db()
