"""
Tests for US-05: Keyword Extraction & Parsing
"""

import pytest
import requests
import json
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8005"

class TestKeywordExtraction:
    """Test class for keyword extraction functionality"""
    
    @classmethod
    def setup_class(cls):
        """Setup test class"""
        cls.analysis_id = None
    
    def test_01_health_check(self):
        """Test health check endpoint"""
        response = requests.get(f"{BASE_URL}/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "US-05" in data["service"]
        print("✓ Health check passed")
    
    def test_02_root_endpoint(self):
        """Test root endpoint"""
        response = requests.get(f"{BASE_URL}/")
        
        assert response.status_code == 200
        data = response.json()
        assert "US-05" in data["message"]
        assert "endpoints" in data
        print("✓ Root endpoint working")
    
    def test_03_extract_keywords_from_resume(self):
        """Test keyword extraction from resume"""
        payload = {
            "document_id": "test-resume-123",
            "document_type": "resume"
        }
        
        response = requests.post(f"{BASE_URL}/extract-keywords", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        assert "analysis_id" in data
        assert data["document_id"] == "test-resume-123"
        assert data["document_type"] == "resume"
        assert "tfidf_keywords" in data
        assert "technical_skills" in data
        assert "text_stats" in data
        
        # Store analysis ID for later tests
        self.__class__.analysis_id = data["analysis_id"]
        print(f"✓ Resume keyword extraction successful, analysis ID: {self.analysis_id}")
    
    def test_04_extract_keywords_from_job_description(self):
        """Test keyword extraction from job description"""
        payload = {
            "document_id": "test-job-456",
            "document_type": "job_description"
        }
        
        response = requests.post(f"{BASE_URL}/extract-keywords", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        assert "analysis_id" in data
        assert data["document_id"] == "test-job-456"
        assert data["document_type"] == "job_description"
        print("✓ Job description keyword extraction successful")
    
    def test_05_analyze_text_comprehensive(self):
        """Test comprehensive text analysis"""
        payload = {
            "text": "I am a Python developer with 5 years of experience in Django, Flask, and FastAPI. I have worked with PostgreSQL, Redis, and AWS cloud services. I have strong leadership and communication skills.",
            "analysis_type": "comprehensive"
        }
        
        response = requests.post(f"{BASE_URL}/analyze-text", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        assert "analysis_id" in data
        assert "technical_skills" in data
        assert "experience_years" in data
        assert "soft_skills" in data
        
        # Verify specific extractions
        technical_skills = data["technical_skills"]
        assert "programming_languages" in technical_skills
        assert "python" in technical_skills["programming_languages"]
        
        assert data["experience_years"] == 5
        
        soft_skills = data["soft_skills"]
        assert "leadership" in soft_skills
        assert "communication" in soft_skills
        
        print("✓ Comprehensive text analysis successful")
        print(f"  - Technical skills found: {len(technical_skills)}")
        print(f"  - Experience years: {data['experience_years']}")
        print(f"  - Soft skills: {len(soft_skills)}")
    
    def test_06_analyze_empty_text(self):
        """Test analysis with empty text"""
        payload = {
            "text": "",
            "analysis_type": "comprehensive"
        }
        
        response = requests.post(f"{BASE_URL}/analyze-text", json=payload)
        
        assert response.status_code == 400
        print("✓ Empty text validation working")
    
    def test_07_list_analyses(self):
        """Test listing all analyses"""
        response = requests.get(f"{BASE_URL}/analyses")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0  # Should have analyses from previous tests
        
        # Check structure of first analysis
        first_analysis = data[0]
        assert "id" in first_analysis
        assert "analysis_type" in first_analysis
        assert "created_at" in first_analysis
        
        print(f"✓ Listed {len(data)} analyses")
    
    def test_08_get_analysis_details(self):
        """Test getting detailed analysis results"""
        if not self.analysis_id:
            pytest.skip("No analysis ID available from previous tests")
        
        response = requests.get(f"{BASE_URL}/analysis/{self.analysis_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == self.analysis_id
        assert "results" in data
        assert "created_at" in data
        
        print("✓ Retrieved analysis details successfully")
    
    def test_09_get_nonexistent_analysis(self):
        """Test getting details for non-existent analysis"""
        fake_id = "non-existent-analysis-id"
        response = requests.get(f"{BASE_URL}/analysis/{fake_id}")
        
        assert response.status_code == 404
        print("✓ Non-existent analysis handling working")
    
    def test_10_technical_skills_extraction(self):
        """Test specific technical skills extraction"""
        test_cases = [
            {
                "text": "I work with React, Angular, and Vue.js frameworks",
                "expected_category": "frameworks",
                "expected_skills": ["react", "angular", "vue"]
            },
            {
                "text": "Database experience includes MySQL, PostgreSQL, and MongoDB",
                "expected_category": "databases", 
                "expected_skills": ["mysql", "postgresql", "mongodb"]
            },
            {
                "text": "Cloud platforms: AWS, Azure, Google Cloud Platform",
                "expected_category": "cloud_platforms",
                "expected_skills": ["aws", "azure", "gcp"]
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            payload = {
                "text": test_case["text"],
                "analysis_type": "comprehensive"
            }
            
            response = requests.post(f"{BASE_URL}/analyze-text", json=payload)
            assert response.status_code == 200
            
            data = response.json()
            technical_skills = data["technical_skills"]
            
            # Check if expected category exists
            assert test_case["expected_category"] in technical_skills
            
            # Check if expected skills are found
            found_skills = technical_skills[test_case["expected_category"]]
            for expected_skill in test_case["expected_skills"]:
                assert expected_skill in found_skills
            
            print(f"✓ Technical skills test case {i+1} passed")
    
    def test_11_job_roles_extraction(self):
        """Test job roles extraction"""
        payload = {
            "text": "I am a software engineer and full stack developer with experience as a data scientist",
            "analysis_type": "comprehensive"
        }
        
        response = requests.post(f"{BASE_URL}/analyze-text", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        job_roles = data["job_roles"]
        assert "software engineer" in job_roles
        assert "full stack developer" in job_roles
        assert "data scientist" in job_roles
        
        print(f"✓ Job roles extraction successful: {job_roles}")
    
    def test_12_text_statistics(self):
        """Test text statistics calculation"""
        test_text = "This is a test sentence. This is another sentence with more words."
        
        payload = {
            "text": test_text,
            "analysis_type": "comprehensive"
        }
        
        response = requests.post(f"{BASE_URL}/analyze-text", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        text_stats = data["text_stats"]
        assert "word_count" in text_stats
        assert "sentence_count" in text_stats
        assert "character_count" in text_stats
        
        # Verify basic statistics
        assert text_stats["sentence_count"] == 2
        assert text_stats["character_count"] == len(test_text)
        assert text_stats["word_count"] > 0
        
        print(f"✓ Text statistics: {text_stats}")

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
