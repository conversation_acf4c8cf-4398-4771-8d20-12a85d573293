"""
Tests for US-06: Matching Score
"""

import pytest
import requests
import json
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8006"

class TestMatchingScore:
    """Test class for matching score functionality"""
    
    @classmethod
    def setup_class(cls):
        """Setup test class"""
        cls.match_id = None
    
    def test_01_health_check(self):
        """Test health check endpoint"""
        response = requests.get(f"{BASE_URL}/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "US-06" in data["service"]
        print("✓ Health check passed")
    
    def test_02_root_endpoint(self):
        """Test root endpoint"""
        response = requests.get(f"{BASE_URL}/")
        
        assert response.status_code == 200
        data = response.json()
        assert "US-06" in data["message"]
        assert "endpoints" in data
        print("✓ Root endpoint working")
    
    def test_03_calculate_matching_score(self):
        """Test matching score calculation"""
        payload = {
            "resume_id": "test-resume-123",
            "job_description_id": "test-job-456"
        }
        
        response = requests.post(f"{BASE_URL}/calculate-match", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        assert "match_id" in data
        assert data["resume_id"] == "test-resume-123"
        assert data["job_description_id"] == "test-job-456"
        assert "overall_score" in data
        assert "detailed_scores" in data
        assert "matching_keywords" in data
        assert "missing_keywords" in data
        assert "skill_matches" in data
        assert "skill_gaps" in data
        assert "recommendations" in data
        
        # Validate score is between 0 and 100
        assert 0 <= data["overall_score"] <= 100
        
        # Store match ID for later tests
        self.__class__.match_id = data["match_id"]
        print(f"✓ Matching score calculated: {data['overall_score']}%")
        print(f"  Match ID: {self.match_id}")
    
    def test_04_detailed_scores_structure(self):
        """Test detailed scores structure"""
        payload = {
            "resume_id": "test-resume-789",
            "job_description_id": "test-job-101"
        }
        
        response = requests.post(f"{BASE_URL}/calculate-match", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        detailed_scores = data["detailed_scores"]
        expected_keys = ['keyword_match', 'skill_match', 'role_match', 'technical_skills', 'frameworks', 'tools']
        
        for key in expected_keys:
            assert key in detailed_scores
            assert isinstance(detailed_scores[key], (int, float))
            assert 0 <= detailed_scores[key] <= 100
        
        print("✓ Detailed scores structure validated")
        print(f"  Keyword match: {detailed_scores['keyword_match']}%")
        print(f"  Skill match: {detailed_scores['skill_match']}%")
        print(f"  Role match: {detailed_scores['role_match']}%")
    
    def test_05_recommendations_generation(self):
        """Test recommendations generation"""
        payload = {
            "resume_id": "test-resume-recommendations",
            "job_description_id": "test-job-recommendations"
        }
        
        response = requests.post(f"{BASE_URL}/calculate-match", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        recommendations = data["recommendations"]
        assert isinstance(recommendations, list)
        assert len(recommendations) <= 5  # Should be limited to 5
        
        # Each recommendation should be a string
        for rec in recommendations:
            assert isinstance(rec, str)
            assert len(rec) > 0
        
        print(f"✓ Generated {len(recommendations)} recommendations")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
    
    def test_06_skill_matching_analysis(self):
        """Test skill matching analysis"""
        payload = {
            "resume_id": "test-resume-skills",
            "job_description_id": "test-job-skills"
        }
        
        response = requests.post(f"{BASE_URL}/calculate-match", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        skill_matches = data["skill_matches"]
        skill_gaps = data["skill_gaps"]
        
        assert isinstance(skill_matches, dict)
        assert isinstance(skill_gaps, dict)
        
        # Validate structure of skill matches and gaps
        for category, skills in skill_matches.items():
            assert isinstance(skills, list)
            for skill in skills:
                assert isinstance(skill, str)
        
        for category, gaps in skill_gaps.items():
            assert isinstance(gaps, list)
            for gap in gaps:
                assert isinstance(gap, str)
        
        print("✓ Skill matching analysis validated")
        print(f"  Skill matches: {len(skill_matches)} categories")
        print(f"  Skill gaps: {len(skill_gaps)} categories")
    
    def test_07_keyword_analysis(self):
        """Test keyword analysis"""
        payload = {
            "resume_id": "test-resume-keywords",
            "job_description_id": "test-job-keywords"
        }
        
        response = requests.post(f"{BASE_URL}/calculate-match", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        matching_keywords = data["matching_keywords"]
        missing_keywords = data["missing_keywords"]
        
        assert isinstance(matching_keywords, list)
        assert isinstance(missing_keywords, list)
        
        # Validate keyword structure
        for keyword in matching_keywords:
            assert isinstance(keyword, str)
            assert len(keyword) > 0
        
        for keyword in missing_keywords:
            assert isinstance(keyword, str)
            assert len(keyword) > 0
        
        print("✓ Keyword analysis validated")
        print(f"  Matching keywords: {len(matching_keywords)}")
        print(f"  Missing keywords: {len(missing_keywords)}")
    
    def test_08_get_matching_history(self):
        """Test getting matching history"""
        response = requests.get(f"{BASE_URL}/matches")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0  # Should have matches from previous tests
        
        # Check structure of first match
        first_match = data[0]
        assert "id" in first_match
        assert "resume_id" in first_match
        assert "job_description_id" in first_match
        assert "overall_score" in first_match
        assert "created_at" in first_match
        
        print(f"✓ Listed {len(data)} matching results")
    
    def test_09_get_match_details(self):
        """Test getting detailed match results"""
        if not self.match_id:
            pytest.skip("No match ID available from previous tests")
        
        response = requests.get(f"{BASE_URL}/match/{self.match_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == self.match_id
        assert "overall_score" in data
        assert "detailed_scores" in data
        assert "matching_keywords" in data
        assert "missing_keywords" in data
        assert "skill_matches" in data
        assert "skill_gaps" in data
        assert "recommendations" in data
        assert "created_at" in data
        
        print("✓ Retrieved match details successfully")
    
    def test_10_get_nonexistent_match(self):
        """Test getting details for non-existent match"""
        fake_id = "non-existent-match-id"
        response = requests.get(f"{BASE_URL}/match/{fake_id}")
        
        assert response.status_code == 404
        print("✓ Non-existent match handling working")
    
    def test_11_score_calculation_consistency(self):
        """Test score calculation consistency"""
        payload = {
            "resume_id": "consistency-test-resume",
            "job_description_id": "consistency-test-job"
        }
        
        # Calculate the same match multiple times
        scores = []
        for i in range(3):
            response = requests.post(f"{BASE_URL}/calculate-match", json=payload)
            assert response.status_code == 200
            data = response.json()
            scores.append(data["overall_score"])
        
        # All scores should be the same (deterministic calculation)
        assert all(score == scores[0] for score in scores)
        print(f"✓ Score calculation is consistent: {scores[0]}%")
    
    def test_12_edge_case_empty_skills(self):
        """Test edge case with documents having minimal content"""
        payload = {
            "resume_id": "minimal-resume",
            "job_description_id": "minimal-job"
        }
        
        response = requests.post(f"{BASE_URL}/calculate-match", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        
        # Should handle minimal content gracefully
        assert 0 <= data["overall_score"] <= 100
        assert isinstance(data["recommendations"], list)
        assert len(data["recommendations"]) > 0  # Should provide some recommendations
        
        print(f"✓ Edge case handling: {data['overall_score']}%")
    
    def test_13_score_range_validation(self):
        """Test that scores are always within valid ranges"""
        test_cases = [
            {"resume_id": "high-match-resume", "job_description_id": "high-match-job"},
            {"resume_id": "low-match-resume", "job_description_id": "low-match-job"},
            {"resume_id": "medium-match-resume", "job_description_id": "medium-match-job"}
        ]
        
        for i, payload in enumerate(test_cases):
            response = requests.post(f"{BASE_URL}/calculate-match", json=payload)
            assert response.status_code == 200
            data = response.json()
            
            # Overall score validation
            assert 0 <= data["overall_score"] <= 100
            
            # Detailed scores validation
            for score_name, score_value in data["detailed_scores"].items():
                assert 0 <= score_value <= 100, f"{score_name} score out of range: {score_value}"
            
            print(f"✓ Test case {i+1} score validation passed: {data['overall_score']}%")

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
