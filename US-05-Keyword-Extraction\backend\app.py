"""
US-05: Keyword Extraction & Parsing Backend
FastAPI application for extracting keywords from uploaded files and parsing text
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
import uuid
from datetime import datetime
import sqlite3
import os

# NLP imports
import re
import nltk
import spacy
from sklearn.feature_extraction.text import TfidfVectorizer
from collections import Counter

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

try:
    nltk.data.find('taggers/averaged_perceptron_tagger')
except LookupError:
    nltk.download('averaged_perceptron_tagger')

from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize, sent_tokenize

# Load spaCy model
try:
    nlp = spacy.load("en_core_web_sm")
except OSError:
    nlp = spacy.blank("en")

app = FastAPI(
    title="US-05: Keyword Extraction & Parsing",
    description="Extract keywords from uploaded files and parse text content",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database setup
DATABASE_PATH = os.path.join(os.path.dirname(__file__), "..", "database", "keyword_extraction.db")

def get_db_connection():
    """Get database connection"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """Initialize database tables"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS keyword_analyses (
                id TEXT PRIMARY KEY,
                document_id TEXT,
                document_type TEXT,
                analysis_type TEXT NOT NULL,
                results_json TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        print("Keyword extraction database initialized successfully")
        
    except Exception as e:
        print(f"Error initializing database: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

# Initialize database on startup
init_database()

# Pydantic models
class KeywordExtractionRequest(BaseModel):
    document_id: str
    document_type: str  # 'resume' or 'job_description'

class TextAnalysisRequest(BaseModel):
    text: str
    analysis_type: Optional[str] = "comprehensive"

class KeywordExtractionResponse(BaseModel):
    analysis_id: str
    document_id: str
    document_type: str
    tfidf_keywords: List[tuple]
    technical_skills: Dict[str, List[str]]
    job_roles: List[str]
    soft_skills: List[str]
    entities: Dict[str, List[str]]
    education: Dict[str, List[str]]
    experience_years: Optional[int]
    text_stats: Dict[str, int]
    created_at: datetime

class TextAnalysisResponse(BaseModel):
    analysis_id: str
    tfidf_keywords: List[tuple]
    technical_skills: Dict[str, List[str]]
    job_roles: List[str]
    soft_skills: List[str]
    entities: Dict[str, List[str]]
    education: Dict[str, List[str]]
    experience_years: Optional[int]
    text_stats: Dict[str, int]
    created_at: datetime

# Technical skills and keywords
TECHNICAL_SKILLS = {
    'programming_languages': [
        'python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust',
        'swift', 'kotlin', 'scala', 'r', 'matlab', 'sql', 'html', 'css', 'bash', 'powershell'
    ],
    'frameworks': [
        'react', 'angular', 'vue', 'django', 'flask', 'fastapi', 'spring', 'express', 'nodejs',
        'laravel', 'rails', 'asp.net', 'tensorflow', 'pytorch', 'keras', 'scikit-learn'
    ],
    'databases': [
        'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch', 'sqlite', 'oracle',
        'sql server', 'cassandra', 'dynamodb', 'firebase'
    ],
    'cloud_platforms': [
        'aws', 'azure', 'gcp', 'google cloud', 'heroku', 'digitalocean', 'kubernetes',
        'docker', 'terraform', 'ansible'
    ],
    'tools': [
        'git', 'github', 'gitlab', 'jenkins', 'jira', 'confluence', 'slack', 'teams',
        'figma', 'sketch', 'photoshop', 'illustrator'
    ]
}

JOB_ROLES = [
    'software engineer', 'software developer', 'full stack developer', 'frontend developer',
    'backend developer', 'data scientist', 'data analyst', 'machine learning engineer',
    'devops engineer', 'product manager', 'project manager', 'ui/ux designer',
    'web developer', 'mobile developer', 'qa engineer', 'test engineer'
]

SOFT_SKILLS = [
    'leadership', 'communication', 'teamwork', 'problem solving', 'analytical thinking',
    'creativity', 'adaptability', 'time management', 'project management',
    'critical thinking', 'collaboration', 'mentoring', 'presentation skills'
]

def clean_text(text: str) -> str:
    """Clean and normalize text for processing"""
    if not text:
        return ""
    
    text = re.sub(r'\s+', ' ', text.strip())
    text = re.sub(r'[^\w\s\-\.\,\;\:\!\?]', ' ', text)
    return text

def extract_keywords_tfidf(text: str, max_features: int = 50) -> List[tuple]:
    """Extract keywords using TF-IDF"""
    if not text:
        return []
    
    try:
        cleaned_text = clean_text(text)
        vectorizer = TfidfVectorizer(
            max_features=max_features,
            stop_words='english',
            ngram_range=(1, 2),
            min_df=1,
            max_df=0.95
        )
        
        tfidf_matrix = vectorizer.fit_transform([cleaned_text])
        feature_names = vectorizer.get_feature_names_out()
        scores = tfidf_matrix.toarray()[0]
        
        keywords = [(feature_names[i], scores[i]) for i in range(len(feature_names)) if scores[i] > 0]
        keywords.sort(key=lambda x: x[1], reverse=True)
        
        return keywords
    except Exception as e:
        print(f"Error in TF-IDF extraction: {e}")
        return []

def extract_technical_skills(text: str) -> Dict[str, List[str]]:
    """Extract technical skills from text"""
    if not text:
        return {}
    
    text_lower = text.lower()
    found_skills = {}
    
    for category, skills in TECHNICAL_SKILLS.items():
        found_in_category = []
        for skill in skills:
            pattern = r'\b' + re.escape(skill.lower()) + r'\b'
            if re.search(pattern, text_lower):
                found_in_category.append(skill)
        
        if found_in_category:
            found_skills[category] = found_in_category
    
    return found_skills

def extract_job_roles(text: str) -> List[str]:
    """Extract job roles from text"""
    if not text:
        return []
    
    text_lower = text.lower()
    found_roles = []
    
    for role in JOB_ROLES:
        pattern = r'\b' + re.escape(role.lower()) + r'\b'
        if re.search(pattern, text_lower):
            found_roles.append(role)
    
    return found_roles

def extract_soft_skills(text: str) -> List[str]:
    """Extract soft skills from text"""
    if not text:
        return []
    
    text_lower = text.lower()
    found_skills = []
    
    for skill in SOFT_SKILLS:
        pattern = r'\b' + re.escape(skill.lower()) + r'\b'
        if re.search(pattern, text_lower):
            found_skills.append(skill)
    
    return found_skills

def extract_entities_spacy(text: str) -> Dict[str, List[str]]:
    """Extract named entities using spaCy"""
    if not text:
        return {}
    
    try:
        doc = nlp(text)
        entities = {}
        
        for ent in doc.ents:
            entity_type = ent.label_
            entity_text = ent.text.strip()
            
            if entity_type not in entities:
                entities[entity_type] = []
            
            if entity_text not in entities[entity_type]:
                entities[entity_type].append(entity_text)
        
        return entities
    except Exception as e:
        print(f"Error in spaCy entity extraction: {e}")
        return {}

def extract_experience_years(text: str) -> Optional[int]:
    """Extract years of experience from text"""
    if not text:
        return None
    
    patterns = [
        r'(\d+)\+?\s*years?\s*(?:of\s*)?experience',
        r'(\d+)\+?\s*years?\s*in',
        r'experience.*?(\d+)\+?\s*years?',
        r'(\d+)\+?\s*yrs?\s*(?:of\s*)?experience',
    ]
    
    years = []
    for pattern in patterns:
        matches = re.findall(pattern, text.lower())
        years.extend([int(match) for match in matches if match.isdigit()])
    
    return max(years) if years else None

def comprehensive_keyword_extraction(text: str) -> Dict[str, Any]:
    """Perform comprehensive keyword and entity extraction"""
    if not text:
        return {}
    
    result = {
        'tfidf_keywords': extract_keywords_tfidf(text, max_features=30),
        'technical_skills': extract_technical_skills(text),
        'job_roles': extract_job_roles(text),
        'soft_skills': extract_soft_skills(text),
        'entities': extract_entities_spacy(text),
        'education': {},  # Simplified for this version
        'experience_years': extract_experience_years(text),
        'text_stats': {
            'word_count': len(text.split()),
            'sentence_count': len(sent_tokenize(text)),
            'character_count': len(text)
        }
    }
    
    return result

# API Endpoints
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "US-05: Keyword Extraction & Parsing Service",
        "version": "1.0.0",
        "endpoints": [
            "/extract-keywords",
            "/analyze-text",
            "/analyses"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "US-05: Keyword Extraction & Parsing",
        "version": "1.0.0"
    }

@app.post("/extract-keywords", response_model=KeywordExtractionResponse)
async def extract_keywords_from_document(request: KeywordExtractionRequest):
    """
    Extract keywords and entities from uploaded documents
    Note: This is a simplified version that works with mock data
    In a real implementation, this would fetch document content from US-03/US-04 services
    """
    
    try:
        # Mock document text for demonstration
        if request.document_type == "resume":
            mock_text = """
            John Doe
            Software Engineer
            
            Experience:
            - 5 years of Python development
            - Worked with Django, Flask, FastAPI
            - Experience with PostgreSQL, Redis
            - AWS cloud deployment
            
            Skills:
            - Python, JavaScript, SQL
            - React, Node.js
            - Docker, Kubernetes
            - Git, Jenkins
            """
        else:
            mock_text = """
            Senior Python Developer
            
            Requirements:
            - 3+ years Python experience
            - Django or Flask framework
            - PostgreSQL database
            - AWS cloud experience
            - Docker containerization
            """
        
        # Perform keyword extraction and analysis
        analysis_results = comprehensive_keyword_extraction(mock_text)
        
        # Generate analysis ID
        analysis_id = str(uuid.uuid4())
        
        # Save results to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO keyword_analyses (
                    id, document_id, document_type, analysis_type, results_json
                ) VALUES (?, ?, ?, ?, ?)
            ''', (
                analysis_id, request.document_id, request.document_type,
                "keyword_extraction", str(analysis_results)
            ))
            
            conn.commit()
            
        finally:
            conn.close()
        
        return KeywordExtractionResponse(
            analysis_id=analysis_id,
            document_id=request.document_id,
            document_type=request.document_type,
            tfidf_keywords=analysis_results.get('tfidf_keywords', []),
            technical_skills=analysis_results.get('technical_skills', {}),
            job_roles=analysis_results.get('job_roles', []),
            soft_skills=analysis_results.get('soft_skills', []),
            entities=analysis_results.get('entities', {}),
            education=analysis_results.get('education', {}),
            experience_years=analysis_results.get('experience_years'),
            text_stats=analysis_results.get('text_stats', {}),
            created_at=datetime.now()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Keyword extraction failed: {str(e)}"
        )

@app.post("/analyze-text", response_model=TextAnalysisResponse)
async def analyze_text_content(request: TextAnalysisRequest):
    """
    Analyze raw text content for keywords and entities
    """
    
    try:
        if not request.text.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Text content cannot be empty"
            )
        
        # Perform analysis
        analysis_results = comprehensive_keyword_extraction(request.text)
        
        # Generate analysis ID
        analysis_id = str(uuid.uuid4())
        
        # Save results to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO keyword_analyses (
                    id, analysis_type, results_json
                ) VALUES (?, ?, ?)
            ''', (
                analysis_id, f"text_analysis_{request.analysis_type}", str(analysis_results)
            ))
            
            conn.commit()
            
        finally:
            conn.close()
        
        return TextAnalysisResponse(
            analysis_id=analysis_id,
            tfidf_keywords=analysis_results.get('tfidf_keywords', []),
            technical_skills=analysis_results.get('technical_skills', {}),
            job_roles=analysis_results.get('job_roles', []),
            soft_skills=analysis_results.get('soft_skills', []),
            entities=analysis_results.get('entities', {}),
            education=analysis_results.get('education', {}),
            experience_years=analysis_results.get('experience_years'),
            text_stats=analysis_results.get('text_stats', {}),
            created_at=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Text analysis failed: {str(e)}"
        )

@app.get("/analyses")
async def list_analyses():
    """
    List all keyword extraction analyses
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT id, document_id, document_type, analysis_type, created_at
            FROM keyword_analyses 
            ORDER BY created_at DESC
        ''')
        
        analyses = cursor.fetchall()
        
        return [
            {
                "id": analysis['id'],
                "document_id": analysis['document_id'],
                "document_type": analysis['document_type'],
                "analysis_type": analysis['analysis_type'],
                "created_at": analysis['created_at']
            }
            for analysis in analyses
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list analyses: {str(e)}"
        )
    finally:
        conn.close()

@app.get("/analysis/{analysis_id}")
async def get_analysis_details(analysis_id: str):
    """
    Get detailed results of a specific analysis
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT * FROM keyword_analyses 
            WHERE id = ?
        ''', (analysis_id,))
        
        analysis = cursor.fetchone()
        
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )
        
        return {
            "id": analysis['id'],
            "document_id": analysis['document_id'],
            "document_type": analysis['document_type'],
            "analysis_type": analysis['analysis_type'],
            "results": analysis['results_json'],
            "created_at": analysis['created_at']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get analysis details: {str(e)}"
        )
    finally:
        conn.close()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8005)
