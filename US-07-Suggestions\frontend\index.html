<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>US-07: Suggestions</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            text-align: center;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .card h2 {
            color: #6f42c1;
            margin-bottom: 1rem;
            border-bottom: 2px solid #6f42c1;
            padding-bottom: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #555;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #6f42c1;
        }

        .btn {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .suggestion-item {
            background: #f8f9fa;
            border-left: 4px solid #6f42c1;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-radius: 5px;
        }

        .suggestion-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #6f42c1;
            margin-bottom: 0.5rem;
        }

        .suggestion-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .priority-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .priority-high {
            background: #dc3545;
            color: white;
        }

        .priority-medium {
            background: #ffc107;
            color: #333;
        }

        .priority-low {
            background: #28a745;
            color: white;
        }

        .category-badge {
            background: #6f42c1;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
        }

        .impact-score {
            background: #e9ecef;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
        }

        .suggestion-description {
            color: #666;
            margin-bottom: 1rem;
            font-style: italic;
        }

        .action-items {
            margin-top: 1rem;
        }

        .action-items h4 {
            color: #6f42c1;
            margin-bottom: 0.5rem;
        }

        .action-item {
            background: white;
            padding: 0.5rem 1rem;
            margin-bottom: 0.5rem;
            border-radius: 3px;
            border-left: 3px solid #6f42c1;
        }

        .quick-wins-section {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 1.5rem;
            border-radius: 5px;
            margin-bottom: 2rem;
        }

        .quick-wins-section h3 {
            color: #007bff;
            margin-bottom: 1rem;
        }

        .quick-win-item {
            background: white;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-radius: 3px;
            border-left: 3px solid #007bff;
        }

        .priority-skills-section {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 1.5rem;
            border-radius: 5px;
            margin-bottom: 2rem;
        }

        .priority-skills-section h3 {
            color: #856404;
            margin-bottom: 1rem;
        }

        .skill-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .skill-tag {
            background: #ffc107;
            color: #333;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .missing-keywords-section {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 1.5rem;
            border-radius: 5px;
            margin-bottom: 2rem;
        }

        .missing-keywords-section h3 {
            color: #721c24;
            margin-bottom: 1rem;
        }

        .keyword-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .keyword-tag {
            background: #dc3545;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #6f42c1;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #dc3545;
            margin-top: 1rem;
        }

        .tabs {
            display: flex;
            margin-bottom: 2rem;
            border-bottom: 2px solid #ddd;
        }

        .tab {
            padding: 1rem 2rem;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab.active {
            border-bottom-color: #6f42c1;
            color: #6f42c1;
            font-weight: bold;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .match-score-display {
            text-align: center;
            margin-bottom: 2rem;
            padding: 1rem;
            background: #e9ecef;
            border-radius: 5px;
        }

        .match-score {
            font-size: 2.5rem;
            font-weight: bold;
            color: #6f42c1;
        }

        .match-score-label {
            color: #666;
            font-size: 1rem;
        }

        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .history-table th,
        .history-table td {
            padding: 0.75rem;
            border: 1px solid #ddd;
            text-align: left;
        }

        .history-table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .history-table tr:hover {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💡 Improvement Suggestions</h1>
            <p>Get personalized recommendations to enhance your resume match score</p>
        </div>

        <div class="tabs">
            <div class="tab active" onclick="switchTab('generate')">Generate Suggestions</div>
            <div class="tab" onclick="switchTab('history')">Suggestion History</div>
        </div>

        <!-- Generate Suggestions Tab -->
        <div id="generate-tab" class="tab-content active">
            <div class="card">
                <h2>🎯 Generate Improvement Suggestions</h2>
                <form id="suggestionsForm">
                    <div class="form-group">
                        <label for="resumeId">Resume ID:</label>
                        <input type="text" id="resumeId" placeholder="Enter resume document ID" required>
                    </div>
                    <div class="form-group">
                        <label for="jobId">Job Description ID:</label>
                        <input type="text" id="jobId" placeholder="Enter job description document ID" required>
                    </div>
                    <button type="submit" class="btn">Generate Suggestions</button>
                </form>

                <div style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 5px;">
                    <p><strong>Alternative:</strong> You can also generate suggestions using an existing match ID from the Matching Score service.</p>
                </div>
            </div>
        </div>

        <!-- History Tab -->
        <div id="history-tab" class="tab-content">
            <div class="card">
                <h2>📈 Suggestion History</h2>
                <button onclick="loadHistory()" class="btn">Refresh History</button>
                <div id="historyResults"></div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="results" class="results-section"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8007';

        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(`${tabName}-tab`).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Form submission
        document.getElementById('suggestionsForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const resumeId = document.getElementById('resumeId').value;
            const jobId = document.getElementById('jobId').value;

            showLoading();

            try {
                const response = await fetch(`${API_BASE}/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        resume_id: resumeId,
                        job_description_id: jobId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                displayResults(data);

            } catch (error) {
                showError(`Error: ${error.message}`);
            }
        });

        function showLoading() {
            document.getElementById('results').innerHTML = `
                <div class="loading">
                    <h3>🔄 Generating Suggestions...</h3>
                    <p>Please wait while we analyze and create personalized recommendations.</p>
                </div>
            `;
        }

        function showError(message) {
            document.getElementById('results').innerHTML = `
                <div class="error">
                    <strong>Error:</strong> ${message}
                </div>
            `;
        }

        function displayResults(data) {
            const resultsDiv = document.getElementById('results');

            let html = `
                <div class="card">
                    <h2>💡 Personalized Suggestions</h2>
                    <p><strong>Suggestion ID:</strong> ${data.suggestion_id}</p>
                    <p><strong>Resume ID:</strong> ${data.resume_id}</p>
                    <p><strong>Job Description ID:</strong> ${data.job_description_id}</p>
                    <p><strong>Generated:</strong> ${new Date(data.created_at).toLocaleString()}</p>

                    <div class="match-score-display">
                        <div class="match-score">${data.overall_match_score}%</div>
                        <div class="match-score-label">Current Match Score</div>
                    </div>
            `;

            // Quick Wins Section
            if (data.quick_wins && data.quick_wins.length > 0) {
                html += `
                    <div class="quick-wins-section">
                        <h3>⚡ Quick Wins</h3>
                        <p>Immediate actions you can take to improve your profile:</p>
                `;

                data.quick_wins.forEach((quickWin, index) => {
                    html += `<div class="quick-win-item">${index + 1}. ${quickWin}</div>`;
                });

                html += `</div>`;
            }

            // Priority Skills Section
            if (data.priority_skills && data.priority_skills.length > 0) {
                html += `
                    <div class="priority-skills-section">
                        <h3>🎯 Priority Skills to Develop</h3>
                        <p>Focus on these high-impact skills:</p>
                        <div class="skill-tags">
                            ${data.priority_skills.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
                        </div>
                    </div>
                `;
            }

            // Missing Keywords Section
            if (data.missing_keywords && data.missing_keywords.length > 0) {
                html += `
                    <div class="missing-keywords-section">
                        <h3>🔍 Missing Keywords</h3>
                        <p>Important keywords from the job description that are missing from your resume:</p>
                        <div class="keyword-tags">
                            ${data.missing_keywords.map(keyword => `<span class="keyword-tag">${keyword}</span>`).join('')}
                        </div>
                    </div>
                `;
            }

            // Detailed Suggestions
            if (data.suggestions && data.suggestions.length > 0) {
                html += `<h3 style="color: #6f42c1; margin: 2rem 0 1rem 0;">📋 Detailed Recommendations</h3>`;

                data.suggestions.forEach((suggestion, index) => {
                    html += `
                        <div class="suggestion-item">
                            <div class="suggestion-title">${index + 1}. ${suggestion.title}</div>
                            <div class="suggestion-meta">
                                <span class="priority-badge priority-${suggestion.priority}">${suggestion.priority.toUpperCase()} PRIORITY</span>
                                <span class="category-badge">${suggestion.category}</span>
                                <span class="impact-score">Impact: ${(suggestion.impact_score * 100).toFixed(0)}%</span>
                            </div>
                            <div class="suggestion-description">${suggestion.description}</div>
                            <div class="action-items">
                                <h4>Action Items:</h4>
                                ${suggestion.action_items.map(action => `<div class="action-item">• ${action}</div>`).join('')}
                            </div>
                        </div>
                    `;
                });
            }

            html += `</div>`;
            resultsDiv.innerHTML = html;
        }

        async function loadHistory() {
            try {
                const response = await fetch(`${API_BASE}/history`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const suggestions = await response.json();

                let html = `
                    <div style="margin-top: 2rem;">
                        <h3 style="color: #6f42c1;">📋 Recent Suggestions (${suggestions.length})</h3>
                `;

                if (suggestions.length === 0) {
                    html += `<p>No suggestions found.</p>`;
                } else {
                    html += `
                        <table class="history-table">
                            <thead>
                                <tr>
                                    <th>Suggestion ID</th>
                                    <th>Resume ID</th>
                                    <th>Job Description ID</th>
                                    <th>Total Suggestions</th>
                                    <th>High Priority</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;

                    suggestions.forEach(suggestion => {
                        html += `
                            <tr>
                                <td style="font-family: monospace;">${suggestion.id.substring(0, 8)}...</td>
                                <td>${suggestion.resume_id}</td>
                                <td>${suggestion.job_description_id}</td>
                                <td>${suggestion.suggestion_count}</td>
                                <td>
                                    <span class="priority-badge priority-high" style="font-size: 0.7rem;">
                                        ${suggestion.high_priority_count}
                                    </span>
                                </td>
                                <td>${new Date(suggestion.created_at).toLocaleString()}</td>
                                <td>
                                    <button onclick="viewSuggestionDetails('${suggestion.id}')" class="btn" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                                        View Details
                                    </button>
                                </td>
                            </tr>
                        `;
                    });

                    html += `</tbody></table>`;
                }

                html += `</div>`;
                document.getElementById('historyResults').innerHTML = html;

            } catch (error) {
                document.getElementById('historyResults').innerHTML = `
                    <div class="error">
                        Error loading history: ${error.message}
                    </div>
                `;
            }
        }

        async function viewSuggestionDetails(suggestionId) {
            try {
                const response = await fetch(`${API_BASE}/suggestion/${suggestionId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                // Switch to generate tab and display results
                switchTab('generate');
                displayResults(data);

            } catch (error) {
                alert(`Error loading suggestion details: ${error.message}`);
            }
        }

        // Load history on page load
        document.addEventListener('DOMContentLoaded', () => {
            // Auto-load history when switching to history tab
            const historyTab = document.querySelector('.tab:nth-child(2)');
            historyTab.addEventListener('click', loadHistory);
        });
    </script>
</body>
</html>
