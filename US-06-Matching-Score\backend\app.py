"""
US-06: Matching Score Backend
FastAPI application for calculating match scores between resumes and job descriptions
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
import uuid
from datetime import datetime
import sqlite3
import os
import requests
import json

app = FastAPI(
    title="US-06: Matching Score",
    description="Calculate match scores between resumes and job descriptions",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
US05_SERVICE_URL = "http://localhost:8005"  # US-05 Keyword Extraction service
DATABASE_PATH = os.path.join(os.path.dirname(__file__), "..", "database", "matching_score.db")

def get_db_connection():
    """Get database connection"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """Initialize database tables"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS matching_results (
                id TEXT PRIMARY KEY,
                resume_id TEXT NOT NULL,
                job_description_id TEXT NOT NULL,
                overall_score REAL NOT NULL,
                detailed_scores TEXT,
                matching_keywords TEXT,
                missing_keywords TEXT,
                skill_matches TEXT,
                skill_gaps TEXT,
                recommendations TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        print("Matching score database initialized successfully")
        
    except Exception as e:
        print(f"Error initializing database: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

# Initialize database on startup
init_database()

# Pydantic models
class MatchingRequest(BaseModel):
    resume_id: str
    job_description_id: str

class MatchingScoreResponse(BaseModel):
    match_id: str
    resume_id: str
    job_description_id: str
    overall_score: float
    detailed_scores: Dict[str, float]
    matching_keywords: List[str]
    missing_keywords: List[str]
    skill_matches: Dict[str, List[str]]
    skill_gaps: Dict[str, List[str]]
    recommendations: List[str]
    created_at: datetime

class MatchingHistoryResponse(BaseModel):
    id: str
    resume_id: str
    job_description_id: str
    overall_score: float
    created_at: datetime

# Utility functions
async def get_keyword_analysis(document_id: str, document_type: str) -> Dict[str, Any]:
    """Get keyword analysis from US-05 service"""
    try:
        # Try to get existing analysis first
        response = requests.post(f"{US05_SERVICE_URL}/extract-keywords", json={
            "document_id": document_id,
            "document_type": document_type
        })
        
        if response.status_code == 200:
            data = response.json()
            return {
                'tfidf_keywords': data.get('tfidf_keywords', []),
                'technical_skills': data.get('technical_skills', {}),
                'job_roles': data.get('job_roles', []),
                'soft_skills': data.get('soft_skills', []),
                'entities': data.get('entities', {}),
                'experience_years': data.get('experience_years')
            }
        else:
            # Return mock data if US-05 service is not available
            return get_mock_analysis(document_type)
            
    except Exception as e:
        print(f"Error getting analysis from US-05: {e}")
        return get_mock_analysis(document_type)

def get_mock_analysis(document_type: str) -> Dict[str, Any]:
    """Get mock analysis data for testing"""
    if document_type == "resume":
        return {
            'tfidf_keywords': [('python', 0.85), ('developer', 0.72), ('experience', 0.68), ('django', 0.65)],
            'technical_skills': {
                'programming_languages': ['python', 'javascript'],
                'frameworks': ['django', 'react', 'fastapi'],
                'databases': ['postgresql', 'redis'],
                'cloud_platforms': ['aws'],
                'tools': ['git', 'docker']
            },
            'job_roles': ['software engineer', 'full stack developer'],
            'soft_skills': ['leadership', 'communication', 'teamwork'],
            'entities': {'PERSON': ['John Doe'], 'ORG': ['Google']},
            'experience_years': 5
        }
    else:  # job_description
        return {
            'tfidf_keywords': [('python', 0.90), ('senior', 0.80), ('developer', 0.75), ('django', 0.70), ('aws', 0.65)],
            'technical_skills': {
                'programming_languages': ['python'],
                'frameworks': ['django', 'flask'],
                'databases': ['postgresql'],
                'cloud_platforms': ['aws', 'docker'],
                'tools': ['git', 'jenkins']
            },
            'job_roles': ['senior python developer', 'software engineer'],
            'soft_skills': ['leadership', 'communication', 'problem solving'],
            'entities': {'ORG': ['Tech Corp']},
            'experience_years': 3
        }

def calculate_keyword_similarity(resume_keywords: List[tuple], job_keywords: List[tuple]) -> Dict[str, Any]:
    """Calculate keyword similarity between resume and job description"""
    resume_kw_set = set([kw[0].lower() for kw in resume_keywords])
    job_kw_set = set([kw[0].lower() for kw in job_keywords])
    
    matching_keywords = list(resume_kw_set.intersection(job_kw_set))
    missing_keywords = list(job_kw_set - resume_kw_set)
    
    if len(job_kw_set) == 0:
        keyword_score = 0.0
    else:
        keyword_score = len(matching_keywords) / len(job_kw_set)
    
    return {
        'score': keyword_score,
        'matching': matching_keywords,
        'missing': missing_keywords
    }

def calculate_skill_similarity(resume_skills: Dict[str, List[str]], job_skills: Dict[str, List[str]]) -> Dict[str, Any]:
    """Calculate skill similarity between resume and job description"""
    skill_matches = {}
    skill_gaps = {}
    category_scores = {}
    
    all_categories = set(resume_skills.keys()) | set(job_skills.keys())
    
    for category in all_categories:
        resume_cat_skills = set([skill.lower() for skill in resume_skills.get(category, [])])
        job_cat_skills = set([skill.lower() for skill in job_skills.get(category, [])])
        
        matches = list(resume_cat_skills.intersection(job_cat_skills))
        gaps = list(job_cat_skills - resume_cat_skills)
        
        if matches:
            skill_matches[category] = matches
        if gaps:
            skill_gaps[category] = gaps
        
        if len(job_cat_skills) == 0:
            category_scores[category] = 1.0 if len(resume_cat_skills) > 0 else 0.0
        else:
            category_scores[category] = len(matches) / len(job_cat_skills)
    
    overall_skill_score = sum(category_scores.values()) / len(category_scores) if category_scores else 0.0
    
    return {
        'score': overall_skill_score,
        'matches': skill_matches,
        'gaps': skill_gaps,
        'category_scores': category_scores
    }

def generate_recommendations(skill_gaps: Dict[str, List[str]], missing_keywords: List[str]) -> List[str]:
    """Generate improvement recommendations"""
    recommendations = []
    
    # Skill-based recommendations
    for category, gaps in skill_gaps.items():
        if gaps:
            category_name = category.replace('_', ' ').title()
            recommendations.append(f"Consider learning {category_name}: {', '.join(gaps[:3])}")
    
    # Keyword-based recommendations
    if missing_keywords:
        top_missing = missing_keywords[:5]
        recommendations.append(f"Include these relevant keywords: {', '.join(top_missing)}")
    
    if len(recommendations) == 0:
        recommendations.append("Great match! Consider highlighting your relevant experience more prominently.")
    elif len(skill_gaps) > 3:
        recommendations.append("Focus on developing skills in the most critical areas first.")
    
    return recommendations[:5]

def calculate_overall_score(keyword_score: float, skill_score: float, role_match: float) -> float:
    """Calculate weighted overall matching score"""
    overall = (skill_score * 0.5) + (keyword_score * 0.3) + (role_match * 0.2)
    return round(min(100.0, overall * 100), 2)

# API Endpoints
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "US-06: Matching Score Service",
        "version": "1.0.0",
        "endpoints": [
            "/calculate-match",
            "/matches",
            "/match/{match_id}"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "US-06: Matching Score",
        "version": "1.0.0"
    }

@app.post("/calculate-match", response_model=MatchingScoreResponse)
async def calculate_matching_score(request: MatchingRequest):
    """
    Calculate match score between resume and job description
    """
    
    try:
        # Get analyses for both documents
        resume_analysis = await get_keyword_analysis(request.resume_id, 'resume')
        job_analysis = await get_keyword_analysis(request.job_description_id, 'job_description')
        
        # Calculate keyword similarity
        keyword_similarity = calculate_keyword_similarity(
            resume_analysis.get('tfidf_keywords', []),
            job_analysis.get('tfidf_keywords', [])
        )
        
        # Calculate skill similarity
        skill_similarity = calculate_skill_similarity(
            resume_analysis.get('technical_skills', {}),
            job_analysis.get('technical_skills', {})
        )
        
        # Calculate role matching
        resume_roles = set([role.lower() for role in resume_analysis.get('job_roles', [])])
        job_roles = set([role.lower() for role in job_analysis.get('job_roles', [])])
        
        if len(job_roles) == 0:
            role_match_score = 0.5
        else:
            role_matches = len(resume_roles.intersection(job_roles))
            role_match_score = role_matches / len(job_roles)
        
        # Calculate overall score
        overall_score = calculate_overall_score(
            keyword_similarity['score'],
            skill_similarity['score'],
            role_match_score
        )
        
        # Generate recommendations
        recommendations = generate_recommendations(
            skill_similarity['gaps'],
            keyword_similarity['missing']
        )
        
        # Create detailed scores
        detailed_scores = {
            'keyword_match': round(keyword_similarity['score'] * 100, 2),
            'skill_match': round(skill_similarity['score'] * 100, 2),
            'role_match': round(role_match_score * 100, 2),
            'technical_skills': round(skill_similarity['category_scores'].get('programming_languages', 0) * 100, 2),
            'frameworks': round(skill_similarity['category_scores'].get('frameworks', 0) * 100, 2),
            'tools': round(skill_similarity['category_scores'].get('tools', 0) * 100, 2)
        }
        
        # Generate match ID and save results
        match_id = str(uuid.uuid4())
        
        # Save matching results
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO matching_results (
                    id, resume_id, job_description_id, overall_score, 
                    detailed_scores, matching_keywords, missing_keywords,
                    skill_matches, skill_gaps, recommendations
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                match_id, request.resume_id, request.job_description_id, overall_score,
                json.dumps(detailed_scores), json.dumps(keyword_similarity['matching']),
                json.dumps(keyword_similarity['missing']), json.dumps(skill_similarity['matches']),
                json.dumps(skill_similarity['gaps']), json.dumps(recommendations)
            ))
            
            conn.commit()
            
        finally:
            conn.close()
        
        return MatchingScoreResponse(
            match_id=match_id,
            resume_id=request.resume_id,
            job_description_id=request.job_description_id,
            overall_score=overall_score,
            detailed_scores=detailed_scores,
            matching_keywords=keyword_similarity['matching'],
            missing_keywords=keyword_similarity['missing'],
            skill_matches=skill_similarity['matches'],
            skill_gaps=skill_similarity['gaps'],
            recommendations=recommendations,
            created_at=datetime.now()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Matching calculation failed: {str(e)}"
        )

@app.get("/matches", response_model=List[MatchingHistoryResponse])
async def get_matching_history():
    """
    Get matching history
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT id, resume_id, job_description_id, overall_score, created_at
            FROM matching_results 
            ORDER BY created_at DESC
        ''')
        
        matches = cursor.fetchall()
        
        return [
            MatchingHistoryResponse(
                id=match['id'],
                resume_id=match['resume_id'],
                job_description_id=match['job_description_id'],
                overall_score=match['overall_score'],
                created_at=datetime.fromisoformat(match['created_at'])
            )
            for match in matches
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get matching history: {str(e)}"
        )
    finally:
        conn.close()

@app.get("/match/{match_id}")
async def get_match_details(match_id: str):
    """
    Get detailed results of a specific match
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT * FROM matching_results 
            WHERE id = ?
        ''', (match_id,))
        
        match = cursor.fetchone()
        
        if not match:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Match not found"
            )
        
        return {
            "id": match['id'],
            "resume_id": match['resume_id'],
            "job_description_id": match['job_description_id'],
            "overall_score": match['overall_score'],
            "detailed_scores": json.loads(match['detailed_scores']) if match['detailed_scores'] else {},
            "matching_keywords": json.loads(match['matching_keywords']) if match['matching_keywords'] else [],
            "missing_keywords": json.loads(match['missing_keywords']) if match['missing_keywords'] else [],
            "skill_matches": json.loads(match['skill_matches']) if match['skill_matches'] else {},
            "skill_gaps": json.loads(match['skill_gaps']) if match['skill_gaps'] else {},
            "recommendations": json.loads(match['recommendations']) if match['recommendations'] else [],
            "created_at": match['created_at']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get match details: {str(e)}"
        )
    finally:
        conn.close()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8006)
