# US-05: Keyword Extraction & Parsing

## Overview
This service provides keyword extraction and text parsing capabilities for the OpenResume AI system. It extracts technical skills, soft skills, job roles, and other entities from uploaded documents and raw text.

## Features
- **Keyword Extraction**: Extract keywords from uploaded resumes and job descriptions using TF-IDF
- **Technical Skills Detection**: Identify programming languages, frameworks, databases, cloud platforms, and tools
- **Job Roles Recognition**: Detect job titles and positions mentioned in text
- **Soft Skills Identification**: Extract soft skills and competencies
- **Entity Recognition**: Use spaCy NLP for named entity recognition
- **Experience Analysis**: Extract years of experience from text
- **Text Statistics**: Provide word count, sentence count, and character count

## API Endpoints

### Health Check
- **GET** `/health` - Service health check
- **GET** `/` - Root endpoint with service information

### Keyword Extraction
- **POST** `/extract-keywords` - Extract keywords from uploaded documents
- **POST** `/analyze-text` - Analyze raw text content
- **GET** `/analyses` - List all keyword extraction analyses
- **GET** `/analysis/{analysis_id}` - Get detailed analysis results

## Request/Response Examples

### Extract Keywords from Document
```json
POST /extract-keywords
{
    "document_id": "resume-123",
    "document_type": "resume"
}
```

Response:
```json
{
    "analysis_id": "uuid-here",
    "document_id": "resume-123",
    "document_type": "resume",
    "tfidf_keywords": [["python", 0.85], ["developer", 0.72]],
    "technical_skills": {
        "programming_languages": ["python", "javascript"],
        "frameworks": ["django", "react"]
    },
    "job_roles": ["software engineer", "full stack developer"],
    "soft_skills": ["leadership", "communication"],
    "entities": {
        "PERSON": ["John Doe"],
        "ORG": ["Google", "Microsoft"]
    },
    "education": {},
    "experience_years": 5,
    "text_stats": {
        "word_count": 250,
        "sentence_count": 15,
        "character_count": 1500
    },
    "created_at": "2024-01-15T10:30:00"
}
```

### Analyze Text Content
```json
POST /analyze-text
{
    "text": "I am a Python developer with 5 years of experience...",
    "analysis_type": "comprehensive"
}
```

## Technical Skills Categories
- **Programming Languages**: Python, Java, JavaScript, TypeScript, C++, etc.
- **Frameworks**: React, Angular, Django, Flask, Spring, etc.
- **Databases**: MySQL, PostgreSQL, MongoDB, Redis, etc.
- **Cloud Platforms**: AWS, Azure, GCP, Docker, Kubernetes, etc.
- **Tools**: Git, Jenkins, Jira, Figma, etc.

## Job Roles Detected
- Software Engineer/Developer
- Full Stack Developer
- Frontend/Backend Developer
- Data Scientist/Analyst
- Machine Learning Engineer
- DevOps Engineer
- Product/Project Manager
- UI/UX Designer
- QA/Test Engineer

## Soft Skills Detected
- Leadership
- Communication
- Teamwork
- Problem Solving
- Analytical Thinking
- Creativity
- Adaptability
- Time Management
- Project Management

## Installation & Setup

### Prerequisites
- Python 3.8+
- pip package manager

### Install Dependencies
```bash
cd US-05-Keyword-Extraction/backend
pip install -r requirements.txt
```

### Download spaCy Model
```bash
python -m spacy download en_core_web_sm
```

### Initialize Database
```bash
cd ../database
python init_db.py
```

### Run the Service
```bash
cd ../backend
python app.py
```

The service will be available at `http://localhost:8005`

## Testing

### Run Tests
```bash
cd ../tests
python -m pytest test_keyword_extraction.py -v
```

### Test Coverage
- Health check and root endpoints
- Keyword extraction from documents
- Text analysis functionality
- Technical skills extraction
- Job roles detection
- Soft skills identification
- Text statistics calculation
- Error handling and validation

## Database Schema

### keyword_analyses Table
```sql
CREATE TABLE keyword_analyses (
    id TEXT PRIMARY KEY,
    document_id TEXT,
    document_type TEXT,
    analysis_type TEXT NOT NULL,
    results_json TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## NLP Technologies Used
- **NLTK**: Natural Language Toolkit for text processing
- **spaCy**: Industrial-strength NLP for entity recognition
- **scikit-learn**: TF-IDF vectorization for keyword extraction
- **Regular Expressions**: Pattern matching for skills and experience

## Configuration
- **Port**: 8005 (configurable)
- **Database**: SQLite (keyword_extraction.db)
- **Max Keywords**: 30 (TF-IDF)
- **N-gram Range**: 1-2 (unigrams and bigrams)

## Error Handling
- Input validation for empty text
- Database connection error handling
- NLP processing error recovery
- HTTP status codes for different error types

## Future Enhancements
- Support for multiple languages
- Custom skill dictionaries
- Machine learning-based classification
- Integration with external APIs
- Real-time processing capabilities
- Advanced entity linking
