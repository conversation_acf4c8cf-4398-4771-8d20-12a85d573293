<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>US-05: Keyword Extraction & Parsing</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .card h2 {
            color: #667eea;
            margin-bottom: 1rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #555;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results {
            margin-top: 2rem;
        }

        .result-section {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }

        .result-section h3 {
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .skill-category {
            margin-bottom: 1rem;
        }

        .skill-category h4 {
            color: #555;
            margin-bottom: 0.5rem;
        }

        .skill-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .skill-tag {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .keyword-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .keyword-item {
            background: #e9ecef;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.9rem;
            border: 1px solid #dee2e6;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: white;
            border-radius: 5px;
            border: 2px solid #667eea;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #667eea;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #dc3545;
            margin-top: 1rem;
        }

        .tabs {
            display: flex;
            margin-bottom: 2rem;
            border-bottom: 2px solid #ddd;
        }

        .tab {
            padding: 1rem 2rem;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: bold;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Keyword Extraction & Parsing</h1>
            <p>Extract keywords, skills, and entities from text and documents</p>
        </div>

        <div class="tabs">
            <div class="tab active" onclick="switchTab('document')">Document Analysis</div>
            <div class="tab" onclick="switchTab('text')">Text Analysis</div>
            <div class="tab" onclick="switchTab('history')">Analysis History</div>
        </div>

        <!-- Document Analysis Tab -->
        <div id="document-tab" class="tab-content active">
            <div class="card">
                <h2>📄 Document Keyword Extraction</h2>
                <form id="documentForm">
                    <div class="form-group">
                        <label for="documentId">Document ID:</label>
                        <input type="text" id="documentId" placeholder="Enter document ID" required>
                    </div>
                    <div class="form-group">
                        <label for="documentType">Document Type:</label>
                        <select id="documentType" required>
                            <option value="">Select document type</option>
                            <option value="resume">Resume</option>
                            <option value="job_description">Job Description</option>
                        </select>
                    </div>
                    <button type="submit" class="btn">Extract Keywords</button>
                </form>
            </div>
        </div>

        <!-- Text Analysis Tab -->
        <div id="text-tab" class="tab-content">
            <div class="card">
                <h2>📝 Text Analysis</h2>
                <form id="textForm">
                    <div class="form-group">
                        <label for="textContent">Text Content:</label>
                        <textarea id="textContent" placeholder="Paste your text here for analysis..." required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="analysisType">Analysis Type:</label>
                        <select id="analysisType">
                            <option value="comprehensive">Comprehensive Analysis</option>
                            <option value="keywords_only">Keywords Only</option>
                            <option value="skills_only">Skills Only</option>
                        </select>
                    </div>
                    <button type="submit" class="btn">Analyze Text</button>
                </form>
            </div>
        </div>

        <!-- History Tab -->
        <div id="history-tab" class="tab-content">
            <div class="card">
                <h2>📊 Analysis History</h2>
                <button onclick="loadHistory()" class="btn">Refresh History</button>
                <div id="historyResults"></div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="results" class="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8005';

        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(`${tabName}-tab`).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Document form submission
        document.getElementById('documentForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const documentId = document.getElementById('documentId').value;
            const documentType = document.getElementById('documentType').value;
            
            showLoading();
            
            try {
                const response = await fetch(`${API_BASE}/extract-keywords`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        document_id: documentId,
                        document_type: documentType
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                displayResults(data);
                
            } catch (error) {
                showError(`Error: ${error.message}`);
            }
        });

        // Text form submission
        document.getElementById('textForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const textContent = document.getElementById('textContent').value;
            const analysisType = document.getElementById('analysisType').value;
            
            showLoading();
            
            try {
                const response = await fetch(`${API_BASE}/analyze-text`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: textContent,
                        analysis_type: analysisType
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                displayResults(data);
                
            } catch (error) {
                showError(`Error: ${error.message}`);
            }
        });

        function showLoading() {
            document.getElementById('results').innerHTML = `
                <div class="loading">
                    <h3>🔄 Analyzing...</h3>
                    <p>Please wait while we process your request.</p>
                </div>
            `;
        }

        function showError(message) {
            document.getElementById('results').innerHTML = `
                <div class="error">
                    <strong>Error:</strong> ${message}
                </div>
            `;
        }

        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            let html = `
                <div class="card">
                    <h2>📊 Analysis Results</h2>
                    <p><strong>Analysis ID:</strong> ${data.analysis_id}</p>
                    <p><strong>Created:</strong> ${new Date(data.created_at).toLocaleString()}</p>
            `;

            // Text Statistics
            if (data.text_stats) {
                html += `
                    <div class="result-section">
                        <h3>📈 Text Statistics</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">${data.text_stats.word_count}</div>
                                <div class="stat-label">Words</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${data.text_stats.sentence_count}</div>
                                <div class="stat-label">Sentences</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${data.text_stats.character_count}</div>
                                <div class="stat-label">Characters</div>
                            </div>
                            ${data.experience_years ? `
                            <div class="stat-item">
                                <div class="stat-value">${data.experience_years}</div>
                                <div class="stat-label">Years Experience</div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            }

            // Technical Skills
            if (data.technical_skills && Object.keys(data.technical_skills).length > 0) {
                html += `
                    <div class="result-section">
                        <h3>💻 Technical Skills</h3>
                `;
                
                for (const [category, skills] of Object.entries(data.technical_skills)) {
                    html += `
                        <div class="skill-category">
                            <h4>${category.replace(/_/g, ' ').toUpperCase()}</h4>
                            <div class="skill-tags">
                                ${skills.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
                            </div>
                        </div>
                    `;
                }
                
                html += `</div>`;
            }

            // Job Roles
            if (data.job_roles && data.job_roles.length > 0) {
                html += `
                    <div class="result-section">
                        <h3>👔 Job Roles</h3>
                        <div class="skill-tags">
                            ${data.job_roles.map(role => `<span class="skill-tag">${role}</span>`).join('')}
                        </div>
                    </div>
                `;
            }

            // Soft Skills
            if (data.soft_skills && data.soft_skills.length > 0) {
                html += `
                    <div class="result-section">
                        <h3>🤝 Soft Skills</h3>
                        <div class="skill-tags">
                            ${data.soft_skills.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
                        </div>
                    </div>
                `;
            }

            // Keywords
            if (data.tfidf_keywords && data.tfidf_keywords.length > 0) {
                html += `
                    <div class="result-section">
                        <h3>🔑 Top Keywords</h3>
                        <div class="keyword-list">
                            ${data.tfidf_keywords.slice(0, 20).map(([keyword, score]) => 
                                `<span class="keyword-item">${keyword} (${score.toFixed(3)})</span>`
                            ).join('')}
                        </div>
                    </div>
                `;
            }

            // Entities
            if (data.entities && Object.keys(data.entities).length > 0) {
                html += `
                    <div class="result-section">
                        <h3>🏷️ Named Entities</h3>
                `;
                
                for (const [entityType, entities] of Object.entries(data.entities)) {
                    html += `
                        <div class="skill-category">
                            <h4>${entityType}</h4>
                            <div class="skill-tags">
                                ${entities.map(entity => `<span class="skill-tag">${entity}</span>`).join('')}
                            </div>
                        </div>
                    `;
                }
                
                html += `</div>`;
            }

            html += `</div>`;
            resultsDiv.innerHTML = html;
        }

        async function loadHistory() {
            try {
                const response = await fetch(`${API_BASE}/analyses`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const analyses = await response.json();
                
                let html = `
                    <div class="result-section">
                        <h3>📋 Recent Analyses (${analyses.length})</h3>
                `;
                
                if (analyses.length === 0) {
                    html += `<p>No analyses found.</p>`;
                } else {
                    html += `<div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; margin-top: 1rem;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 0.75rem; border: 1px solid #ddd;">ID</th>
                                    <th style="padding: 0.75rem; border: 1px solid #ddd;">Document ID</th>
                                    <th style="padding: 0.75rem; border: 1px solid #ddd;">Type</th>
                                    <th style="padding: 0.75rem; border: 1px solid #ddd;">Analysis Type</th>
                                    <th style="padding: 0.75rem; border: 1px solid #ddd;">Created</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;
                    
                    analyses.forEach(analysis => {
                        html += `
                            <tr>
                                <td style="padding: 0.75rem; border: 1px solid #ddd; font-family: monospace;">${analysis.id.substring(0, 8)}...</td>
                                <td style="padding: 0.75rem; border: 1px solid #ddd;">${analysis.document_id || 'N/A'}</td>
                                <td style="padding: 0.75rem; border: 1px solid #ddd;">${analysis.document_type || 'Text'}</td>
                                <td style="padding: 0.75rem; border: 1px solid #ddd;">${analysis.analysis_type}</td>
                                <td style="padding: 0.75rem; border: 1px solid #ddd;">${new Date(analysis.created_at).toLocaleString()}</td>
                            </tr>
                        `;
                    });
                    
                    html += `</tbody></table></div>`;
                }
                
                html += `</div>`;
                document.getElementById('historyResults').innerHTML = html;
                
            } catch (error) {
                document.getElementById('historyResults').innerHTML = `
                    <div class="error">
                        Error loading history: ${error.message}
                    </div>
                `;
            }
        }

        // Load history on page load
        document.addEventListener('DOMContentLoaded', () => {
            // Auto-load history when switching to history tab
            const historyTab = document.querySelector('.tab:nth-child(3)');
            historyTab.addEventListener('click', loadHistory);
        });
    </script>
</body>
</html>
